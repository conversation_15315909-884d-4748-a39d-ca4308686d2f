/*! firebase-admin v13.4.0 */
"use strict";
/*!
 * Copyright 2020 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isUidIdentifier = isUidIdentifier;
exports.isEmailIdentifier = isEmailIdentifier;
exports.isPhoneIdentifier = isPhoneIdentifier;
exports.isProviderIdentifier = isProviderIdentifier;
/*
 * User defined type guards. See
 * https://www.typescriptlang.org/docs/handbook/advanced-types.html#user-defined-type-guards
 */
function isUidIdentifier(id) {
    return id.uid !== undefined;
}
function isEmailIdentifier(id) {
    return id.email !== undefined;
}
function isPhoneIdentifier(id) {
    return id.phoneNumber !== undefined;
}
function isProviderIdentifier(id) {
    const pid = id;
    return pid.providerId !== undefined && pid.providerUid !== undefined;
}
