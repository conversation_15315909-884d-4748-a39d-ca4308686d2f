{"version": 3, "file": "Exception.js", "sourceRoot": "", "sources": ["../../../src/common/Exception.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\ninterface ExceptionWithCode {\n  code: string | number;\n  name?: string;\n  message?: string;\n  stack?: string;\n}\n\ninterface ExceptionWithMessage {\n  code?: string | number;\n  message: string;\n  name?: string;\n  stack?: string;\n}\n\ninterface ExceptionWithName {\n  code?: string | number;\n  message?: string;\n  name: string;\n  stack?: string;\n}\n\n/**\n * Defines Exception.\n *\n * string or an object with one of (message or name or code) and optional stack\n */\nexport type Exception =\n  | ExceptionWithCode\n  | ExceptionWithMessage\n  | ExceptionWithName\n  | string;\n"]}