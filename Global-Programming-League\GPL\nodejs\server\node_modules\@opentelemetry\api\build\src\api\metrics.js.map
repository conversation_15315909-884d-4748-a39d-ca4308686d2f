{"version": 3, "file": "metrics.js", "sourceRoot": "", "sources": ["../../../src/api/metrics.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,oEAAmE;AACnE,2DAIkC;AAClC,iCAAiC;AAEjC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAE3B;;GAEG;AACH,MAAa,UAAU;IAGrB,+FAA+F;IAC/F,gBAAuB,CAAC;IAExB,oDAAoD;IAC7C,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,QAAuB;QACnD,OAAO,IAAA,6BAAc,EAAC,QAAQ,EAAE,QAAQ,EAAE,cAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,IAAA,wBAAS,EAAC,QAAQ,CAAC,IAAI,uCAAmB,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,QAAQ,CACb,IAAY,EACZ,OAAgB,EAChB,OAAsB;QAEtB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,uCAAuC;IAChC,OAAO;QACZ,IAAA,+BAAgB,EAAC,QAAQ,EAAE,cAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;CACF;AA7CD,gCA6CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter, MeterOptions } from '../metrics/Meter';\nimport { MeterProvider } from '../metrics/MeterProvider';\nimport { NOOP_METER_PROVIDER } from '../metrics/NoopMeterProvider';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'metrics';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Metrics API\n */\nexport class MetricsAPI {\n  private static _instance?: MetricsAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Metrics API */\n  public static getInstance(): MetricsAPI {\n    if (!this._instance) {\n      this._instance = new MetricsAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current global meter provider.\n   * Returns true if the meter provider was successfully registered, else false.\n   */\n  public setGlobalMeterProvider(provider: MeterProvider): boolean {\n    return registerGlobal(API_NAME, provider, DiagAPI.instance());\n  }\n\n  /**\n   * Returns the global meter provider.\n   */\n  public getMeterProvider(): MeterProvider {\n    return getGlobal(API_NAME) || NOOP_METER_PROVIDER;\n  }\n\n  /**\n   * Returns a meter from the global meter provider.\n   */\n  public getMeter(\n    name: string,\n    version?: string,\n    options?: MeterOptions\n  ): Meter {\n    return this.getMeterProvider().getMeter(name, version, options);\n  }\n\n  /** Remove the global meter provider */\n  public disable(): void {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n}\n"]}