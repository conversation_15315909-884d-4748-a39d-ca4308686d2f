{"version": 3, "file": "span.js", "sourceRoot": "", "sources": ["../../../src/trace/span.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '../common/Exception';\nimport { TimeInput } from '../common/Time';\nimport { SpanAttributes, SpanAttributeValue } from './attributes';\nimport { SpanContext } from './span_context';\nimport { SpanStatus } from './status';\nimport { Link } from './link';\n\n/**\n * An interface that represents a span. A span represents a single operation\n * within a trace. Examples of span might include remote procedure calls or a\n * in-process function calls to sub-components. A Trace has a single, top-level\n * \"root\" Span that in turn may have zero or more child Spans, which in turn\n * may have children.\n *\n * Spans are created by the {@link Tracer.startSpan} method.\n */\nexport interface Span {\n  /**\n   * Returns the {@link SpanContext} object associated with this Span.\n   *\n   * Get an immutable, serializable identifier for this span that can be used\n   * to create new child spans. Returned SpanContext is usable even after the\n   * span ends.\n   *\n   * @returns the SpanContext object associated with this Span.\n   */\n  spanContext(): SpanContext;\n\n  /**\n   * Sets an attribute to the span.\n   *\n   * Sets a single Attribute with the key and value passed as arguments.\n   *\n   * @param key the key for this attribute.\n   * @param value the value for this attribute. Setting a value null or\n   *              undefined is invalid and will result in undefined behavior.\n   */\n  setAttribute(key: string, value: SpanAttributeValue): this;\n\n  /**\n   * Sets attributes to the span.\n   *\n   * @param attributes the attributes that will be added.\n   *                   null or undefined attribute values\n   *                   are invalid and will result in undefined behavior.\n   */\n  setAttributes(attributes: SpanAttributes): this;\n\n  /**\n   * Adds an event to the Span.\n   *\n   * @param name the name of the event.\n   * @param [attributesOrStartTime] the attributes that will be added; these are\n   *     associated with this event. Can be also a start time\n   *     if type is {@type TimeInput} and 3rd param is undefined\n   * @param [startTime] start time of the event.\n   */\n  addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | TimeInput,\n    startTime?: TimeInput\n  ): this;\n\n  /**\n   * Adds a single link to the span.\n   *\n   * Links added after the creation will not affect the sampling decision.\n   * It is preferred span links be added at span creation.\n   *\n   * @param link the link to add.\n   */\n  addLink(link: Link): this;\n\n  /**\n   * Adds multiple links to the span.\n   *\n   * Links added after the creation will not affect the sampling decision.\n   * It is preferred span links be added at span creation.\n   *\n   * @param links the links to add.\n   */\n  addLinks(links: Link[]): this;\n\n  /**\n   * Sets a status to the span. If used, this will override the default Span\n   * status. Default is {@link SpanStatusCode.UNSET}. SetStatus overrides the value\n   * of previous calls to SetStatus on the Span.\n   *\n   * @param status the SpanStatus to set.\n   */\n  setStatus(status: SpanStatus): this;\n\n  /**\n   * Updates the Span name.\n   *\n   * This will override the name provided via {@link Tracer.startSpan}.\n   *\n   * Upon this update, any sampling behavior based on Span name will depend on\n   * the implementation.\n   *\n   * @param name the Span name.\n   */\n  updateName(name: string): this;\n\n  /**\n   * Marks the end of Span execution.\n   *\n   * Call to End of a Span MUST not have any effects on child spans. Those may\n   * still be running and can be ended later.\n   *\n   * Do not return `this`. The Span generally should not be used after it\n   * is ended so chaining is not desired in this context.\n   *\n   * @param [endTime] the time to set as Span's end time. If not provided,\n   *     use the current time as the span's end time.\n   */\n  end(endTime?: TimeInput): void;\n\n  /**\n   * Returns the flag whether this span will be recorded.\n   *\n   * @returns true if this Span is active and recording information like events\n   *     with the `AddEvent` operation and attributes using `setAttributes`.\n   */\n  isRecording(): boolean;\n\n  /**\n   * Sets exception as a span event\n   * @param exception the exception the only accepted values are string or Error\n   * @param [time] the time to set as Span's event time. If not provided,\n   *     use the current time.\n   */\n  recordException(exception: Exception, time?: TimeInput): void;\n}\n"]}