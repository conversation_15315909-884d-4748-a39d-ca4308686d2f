import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Global Programming League - Competitive Programming Platform",
  description: "Join the Global Programming League - the premier competitive programming platform for developers worldwide. Participate in tournaments, improve your skills, and connect with the programming community.",
  keywords: "competitive programming, coding contests, programming tournaments, GPL, Global Programming League",
  authors: [{ name: "Global Programming League" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
