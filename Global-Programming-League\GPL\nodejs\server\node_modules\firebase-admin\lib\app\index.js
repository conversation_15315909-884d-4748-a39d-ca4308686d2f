/*! firebase-admin v13.4.0 */
"use strict";
/*!
 * @license
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SDK_VERSION = exports.AppErrorCodes = exports.FirebaseAppError = exports.refreshToken = exports.cert = exports.applicationDefault = exports.deleteApp = exports.getApps = exports.getApp = exports.initializeApp = void 0;
const utils_1 = require("../utils");
var lifecycle_1 = require("./lifecycle");
Object.defineProperty(exports, "initializeApp", { enumerable: true, get: function () { return lifecycle_1.initializeApp; } });
Object.defineProperty(exports, "getApp", { enumerable: true, get: function () { return lifecycle_1.getApp; } });
Object.defineProperty(exports, "getApps", { enumerable: true, get: function () { return lifecycle_1.getApps; } });
Object.defineProperty(exports, "deleteApp", { enumerable: true, get: function () { return lifecycle_1.deleteApp; } });
var credential_factory_1 = require("./credential-factory");
Object.defineProperty(exports, "applicationDefault", { enumerable: true, get: function () { return credential_factory_1.applicationDefault; } });
Object.defineProperty(exports, "cert", { enumerable: true, get: function () { return credential_factory_1.cert; } });
Object.defineProperty(exports, "refreshToken", { enumerable: true, get: function () { return credential_factory_1.refreshToken; } });
var error_1 = require("../utils/error");
Object.defineProperty(exports, "FirebaseAppError", { enumerable: true, get: function () { return error_1.FirebaseAppError; } });
Object.defineProperty(exports, "AppErrorCodes", { enumerable: true, get: function () { return error_1.AppErrorCodes; } });
exports.SDK_VERSION = (0, utils_1.getSdkVersion)();
