{"version": 3, "file": "TextMapPropagator.js", "sourceRoot": "", "sources": ["../../../src/propagation/TextMapPropagator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAkGU,QAAA,oBAAoB,GAAkB;IACjD,GAAG,CAAC,OAAO,EAAE,GAAG;QACd,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,OAAO;QACV,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,EAAE,CAAC;SACX;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;CACF,CAAC;AAEW,QAAA,oBAAoB,GAAkB;IACjD,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK;QACrB,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO;SACR;QAED,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\n\n/**\n * Injects `Context` into and extracts it from carriers that travel\n * in-band across process boundaries. Encoding is expected to conform to the\n * HTTP Header Field semantics. Values are often encoded as RPC/HTTP request\n * headers.\n *\n * The carrier of propagated data on both the client (injector) and server\n * (extractor) side is usually an object such as http headers. Propagation is\n * usually implemented via library-specific request interceptors, where the\n * client-side injects values and the server-side extracts them.\n */\nexport interface TextMapPropagator<Carrier = any> {\n  /**\n   * Injects values from a given `Context` into a carrier.\n   *\n   * OpenTelemetry defines a common set of format values (TextMapPropagator),\n   * and each has an expected `carrier` type.\n   *\n   * @param context the Context from which to extract values to transmit over\n   *     the wire.\n   * @param carrier the carrier of propagation fields, such as http request\n   *     headers.\n   * @param setter an optional {@link TextMapSetter}. If undefined, values will be\n   *     set by direct object assignment.\n   */\n  inject(\n    context: Context,\n    carrier: Carrier,\n    setter: TextMapSetter<Carrier>\n  ): void;\n\n  /**\n   * Given a `Context` and a carrier, extract context values from a\n   * carrier and return a new context, created from the old context, with the\n   * extracted values.\n   *\n   * @param context the Context from which to extract values to transmit over\n   *     the wire.\n   * @param carrier the carrier of propagation fields, such as http request\n   *     headers.\n   * @param getter an optional {@link TextMapGetter}. If undefined, keys will be all\n   *     own properties, and keys will be accessed by direct object access.\n   */\n  extract(\n    context: Context,\n    carrier: Carrier,\n    getter: TextMapGetter<Carrier>\n  ): Context;\n\n  /**\n   * Return a list of all fields which may be used by the propagator.\n   */\n  fields(): string[];\n}\n\n/**\n * A setter is specified by the caller to define a specific method\n * to set key/value pairs on the carrier within a propagator.\n */\nexport interface TextMapSetter<Carrier = any> {\n  /**\n   * Callback used to set a key/value pair on an object.\n   *\n   * Should be called by the propagator each time a key/value pair\n   * should be set, and should set that key/value pair on the propagator.\n   *\n   * @param carrier object or class which carries key/value pairs\n   * @param key string key to modify\n   * @param value value to be set to the key on the carrier\n   */\n  set(carrier: Carrier, key: string, value: string): void;\n}\n\n/**\n * A getter is specified by the caller to define a specific method\n * to get the value of a key from a carrier.\n */\nexport interface TextMapGetter<Carrier = any> {\n  /**\n   * Get a list of all keys available on the carrier.\n   *\n   * @param carrier\n   */\n  keys(carrier: Carrier): string[];\n\n  /**\n   * Get the value of a specific key from the carrier.\n   *\n   * @param carrier\n   * @param key\n   */\n  get(carrier: Carrier, key: string): undefined | string | string[];\n}\n\nexport const defaultTextMapGetter: TextMapGetter = {\n  get(carrier, key) {\n    if (carrier == null) {\n      return undefined;\n    }\n    return carrier[key];\n  },\n\n  keys(carrier) {\n    if (carrier == null) {\n      return [];\n    }\n    return Object.keys(carrier);\n  },\n};\n\nexport const defaultTextMapSetter: TextMapSetter = {\n  set(carrier, key, value) {\n    if (carrier == null) {\n      return;\n    }\n\n    carrier[key] = value;\n  },\n};\n"]}