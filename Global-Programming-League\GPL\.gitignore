.dart_tool/
.idea/
.vscode/
build/
*.iml
*.log
*.lock
pubspec.lock

# Flutter/Dart/Pub related
.dart_tool/
.packages
.pub-cache/
build/
pubspec.lock

# Visual Studio Code
.vscode/

# IntelliJ
.idea/
*.iml

# macOS
.DS_Store

# Windows
Thumbs.db

# Others
*.log
*.tmp

# Node.js dependencies
node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm/

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# TypeScript cache
*.tsbuildinfo

# dotenv environment variable files
.env
.env.*

# Firebase service account credentials
firebase-service-account.json
firebase-service-account.json.json
*service-account*.json

# IDE/editor settings
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# OS-specific files
.DS_Store
Thumbs.db
